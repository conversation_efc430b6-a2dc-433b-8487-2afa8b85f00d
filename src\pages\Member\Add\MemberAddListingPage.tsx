import { useState, useRef, useEffect, Fragment, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { MemberWrapper } from "../../../components/MemberWrapper";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { useSDK } from "../../../hooks/useSDK";
import { useToast } from "../../../hooks/useToast";
import { Modal } from "../../../components/Modal";

interface IListingForm {
  // Basic Info
  listingType: string;
  title: string;
  description: string;
  category: string;
  images: File[];

  // Price & Stock
  price: string;
  discountPrice: string;
  quantity: string;

  // Location
  addressLine: string;
  city: string;
  country: string;

  // Shipping
  length: string;
  width: string;
  height: string;
  weight: string;
  fragileHandling: boolean;
  flammableGas: boolean;
  liquidItems: boolean;
  glassBreakable: boolean;
  ebaDelivery: boolean;
  localDelivery: boolean;
  internationalDelivery: boolean;
  pickup: boolean;
  meetup: boolean;
  sizes: Record<string, { selected: boolean; quantity: string }>;
  // Service Fields
  schedulingType: "slots" | "flexible";
  availability: Record<
    string,
    { selected: boolean; slots: { start: string; end: string }[] }
  >;
  serviceLocationType: "provider" | "buyer" | "choice";
  paymentTerms: "before" | "after";
}

const MemberAddListingPage = () => {
  const navigate = useNavigate();
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [isSuggestModalOpen, setIsSuggestModalOpen] = useState(false);
  const [suggestedCategory, setSuggestedCategory] = useState("");
  const [justPrefilled, setJustPrefilled] = useState(false);

  const availableSizes = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"];
  const daysOfWeek = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];

  const initialSizes = availableSizes.reduce(
    (acc, size) => {
      acc[size] = { selected: false, quantity: "" };
      return acc;
    },
    {} as Record<string, { selected: boolean; quantity: string }>
  );

  const initialAvailability = daysOfWeek.reduce(
    (acc, day) => {
      acc[day] = {
        selected: false,
        slots: [{ start: "09:00", end: "18:00" }],
      };
      return acc;
    },
    {} as Record<
      string,
      { selected: boolean; slots: { start: string; end: string }[] }
    >
  );

  const prefillListings: IListingForm[] = [
    {
      listingType: "Item",
      title: "Wireless Headphones",
      description: "High-quality wireless headphones with noise cancellation.",
      category: "electronics",
      images: [],
      price: "99.99",
      discountPrice: "79.99",
      quantity: "10",
      addressLine: "456 Tech Avenue, Suite 100",
      city: "San Francisco",
      country: "United States",
      length: "20",
      width: "15",
      height: "8",
      weight: "0.5",
      fragileHandling: true,
      flammableGas: false,
      liquidItems: false,
      glassBreakable: false,
      ebaDelivery: true,
      localDelivery: true,
      internationalDelivery: false,
      pickup: true,
      meetup: false,
      sizes: initialSizes,
      schedulingType: "slots",
      availability: initialAvailability,
      serviceLocationType: "provider",
      paymentTerms: "before",
    },
    {
      listingType: "Item",
      title: "Designer T-Shirt",
      description: "100% cotton designer t-shirt, available in all sizes.",
      category: "clothing",
      images: [],
      price: "29.99",
      discountPrice: "24.99",
      quantity: "50",
      addressLine: "789 Fashion Blvd, Apt 12C",
      city: "Los Angeles",
      country: "United States",
      length: "30",
      width: "25",
      height: "2",
      weight: "0.2",
      fragileHandling: false,
      flammableGas: false,
      liquidItems: false,
      glassBreakable: false,
      ebaDelivery: false,
      localDelivery: true,
      internationalDelivery: true,
      pickup: false,
      meetup: true,
      sizes: {
        ...initialSizes,
        S: { selected: true, quantity: "10" },
        M: { selected: true, quantity: "15" },
        L: { selected: true, quantity: "20" },
      },
      schedulingType: "slots",
      availability: initialAvailability,
      serviceLocationType: "provider",
      paymentTerms: "before",
    },
    {
      listingType: "Service",
      title: "Home Cleaning Service",
      description: "Professional home cleaning service, available weekdays.",
      category: "services",
      images: [],
      price: "60.00",
      discountPrice: "50.00",
      quantity: "1",
      addressLine: "321 Clean St, Floor 2",
      city: "Chicago",
      country: "United States",
      length: "0.0",
      width: "0.0",
      height: "0.0",
      weight: "0.0",
      fragileHandling: false,
      flammableGas: false,
      liquidItems: false,
      glassBreakable: false,
      ebaDelivery: false,
      localDelivery: false,
      internationalDelivery: false,
      pickup: false,
      meetup: false,
      sizes: initialSizes,
      schedulingType: "slots",
      availability: {
        ...initialAvailability,
        Monday: { selected: true, slots: [{ start: "09:00", end: "17:00" }] },
        Wednesday: {
          selected: true,
          slots: [{ start: "09:00", end: "17:00" }],
        },
        Friday: { selected: true, slots: [{ start: "09:00", end: "17:00" }] },
      },
      serviceLocationType: "buyer",
      paymentTerms: "after",
    },
    {
      listingType: "Item",
      title: "Mountain Bike",
      description: "Durable mountain bike, suitable for all terrains.",
      category: "sports",
      images: [],
      price: "350.00",
      discountPrice: "299.99",
      quantity: "5",
      addressLine: "654 Adventure Rd, Garage 3",
      city: "Denver",
      country: "United States",
      length: "150",
      width: "25",
      height: "80",
      weight: "14",
      fragileHandling: false,
      flammableGas: false,
      liquidItems: false,
      glassBreakable: false,
      ebaDelivery: true,
      localDelivery: false,
      internationalDelivery: false,
      pickup: true,
      meetup: true,
      sizes: initialSizes,
      schedulingType: "slots",
      availability: initialAvailability,
      serviceLocationType: "provider",
      paymentTerms: "before",
    },
    {
      listingType: "Service",
      title: "Math Tutoring",
      description:
        "Experienced math tutor for high school and college students.",
      category: "education",
      images: [],
      price: "40.00",
      discountPrice: "35.00",
      quantity: "1",
      addressLine: "987 Study Lane, Room 5",
      city: "Boston",
      country: "United States",
      length: "0.0",
      width: "0.0",
      height: "0.0",
      weight: "0.0",
      fragileHandling: false,
      flammableGas: false,
      liquidItems: false,
      glassBreakable: false,
      ebaDelivery: false,
      localDelivery: false,
      internationalDelivery: false,
      pickup: false,
      meetup: false,
      sizes: initialSizes,
      schedulingType: "slots",
      availability: {
        ...initialAvailability,
        Tuesday: { selected: true, slots: [{ start: "15:00", end: "18:00" }] },
        Thursday: { selected: true, slots: [{ start: "15:00", end: "18:00" }] },
      },
      serviceLocationType: "choice",
      paymentTerms: "before",
    },
  ];

  const [prefillIndex, setPrefillIndex] = useState(0);

  const handlePrefill = () => {
    let clone: IListingForm;
    if (typeof structuredClone === "function") {
      clone = structuredClone(prefillListings[prefillIndex]);
    } else {
      clone = JSON.parse(JSON.stringify(prefillListings[prefillIndex]));
    }
    setFormData(clone);
    setPrefillIndex((prev) => (prev + 1) % prefillListings.length);
    setJustPrefilled(true);
  };

  useEffect(() => {
    if (justPrefilled) {
      const timer = setTimeout(() => setJustPrefilled(false), 200);
      return () => clearTimeout(timer);
    }
  }, [justPrefilled]);

  const [formData, setFormData] = useState<IListingForm>({
    // Basic Info
    listingType: "Item",
    title: "3D Camera for Sale",
    description: "",
    category: "",
    images: [],

    // Price & Stock
    price: "0.00",
    discountPrice: "0.00",
    quantity: "1",

    // Location
    addressLine: "123 Main Street, Apt 4B",
    city: "New York",
    country: "United States",

    // Shipping
    length: "0.0",
    width: "0.0",
    height: "0.0",
    weight: "0.0",
    fragileHandling: false,
    flammableGas: false,
    liquidItems: false,
    glassBreakable: false,
    ebaDelivery: false,
    localDelivery: false,
    internationalDelivery: false,
    pickup: false,
    meetup: false,
    sizes: initialSizes,
    // Service Fields
    schedulingType: "slots",
    availability: initialAvailability,
    serviceLocationType: "provider",
    paymentTerms: "before",
  });

  // Image upload states
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);
  const [uploadingImages, setUploadingImages] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [categories, setCategories] = useState([
    { value: "", label: "Select a category" },
  ]);

  const sectionRefs = [
    useRef<HTMLDivElement>(null),
    useRef<HTMLDivElement>(null),
    useRef<HTMLDivElement>(null),
    useRef<HTMLDivElement>(null),
    useRef<HTMLDivElement>(null),
  ];

  const itemStepData = [
    { id: 1, title: "Basic Info" },
    { id: 2, title: "Price & Stock" },
    { id: 3, title: "Location" },
    { id: 4, title: "Shipping" },
    { id: 5, title: "Review" },
  ];

  const serviceStepData = [
    { id: 1, title: "Basic Info" },
    { id: 2, title: "Price & Slots" },
    { id: 3, title: "Service Area" },
    { id: 4, title: "Service Details" },
    { id: 5, title: "Review" },
  ];

  const stepData =
    formData.listingType === "Service" ? serviceStepData : itemStepData;

  // Load categories on component mount
  const loadCategories = useCallback(async () => {
    try {
      setError(null);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/member/categories",
        method: "GET",
      });

      console.log("Categories API Response:", response);

      if (!response.error && response.data) {
        const categoryOptions = [
          { value: "", label: "Select a category" },
          ...response.data.map((cat: any) => ({
            value: String(cat.name || "").toLowerCase(),
            label: String(cat.name || ""),
          })),
        ];
        setCategories(categoryOptions);
      } else {
        console.error("Error loading categories:", response.message);
        // Keep default categories if API fails
        setError(String(response.message || "Failed to load categories"));
      }
    } catch (error: any) {
      console.error("Error loading categories:", error);
      setError(String(error?.message || "Failed to load categories"));
      // Keep default categories if API fails
    }
  }, []);

  useEffect(() => {
    loadCategories();
  }, []);

  // Cleanup object URLs on unmount
  useEffect(() => {
    return () => {
      imagePreviewUrls.forEach((url) => URL.revokeObjectURL(url));
    };
  }, [imagePreviewUrls]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = sectionRefs.findIndex(
              (ref) => ref.current === entry.target
            );
            if (index !== -1) {
              setCurrentStep(index + 1);
            }
          }
        });
      },
      {
        rootMargin: "-50% 0px -50% 0px",
        threshold: 0,
      }
    );

    sectionRefs.forEach((ref) => {
      if (ref.current) {
        observer.observe(ref.current);
      }
    });

    return () => {
      sectionRefs.forEach((ref) => {
        if (ref.current) {
          observer.unobserve(ref.current);
        }
      });
    };
  }, [sectionRefs]);

  const handleInputChange = (
    field: keyof IListingForm,
    value: string | boolean | File[]
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSizeChange = (
    size: string,
    field: "selected" | "quantity",
    value: boolean | string
  ) => {
    setFormData((prev) => ({
      ...prev,
      sizes: {
        ...prev.sizes,
        [size]: {
          ...prev.sizes[size],
          [field]: value,
        },
      },
    }));
  };

  const handleSuggestCategory = useCallback(async () => {
    if (!suggestedCategory.trim()) {
      showError("Please enter a category name.");
      return;
    }

    try {
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/member/category-suggestions",
        method: "POST",
        body: { name: suggestedCategory.trim() },
      });

      console.log("Category Suggestion API Response:", response);

      if (!response.error) {
        success(
          "Thank you for your suggestion! Our team will review it shortly."
        );
        setIsSuggestModalOpen(false);
        setSuggestedCategory("");
      } else {
        showError(String(response.message || "Failed to submit suggestion"));
      }
    } catch (error: any) {
      console.error("Error submitting category suggestion:", error);
      showError(String(error?.message || "Failed to submit suggestion"));
    }
  }, []);

  const handleAvailabilityChange = (
    day: string,
    field: "selected" | "slot",
    value: boolean | { index: number; type: "start" | "end"; time: string }
  ) => {
    setFormData((prev) => {
      const newAvailability = { ...prev.availability };
      if (field === "selected") {
        newAvailability[day].selected = value as boolean;
      } else if (field === "slot" && typeof value === "object") {
        const { index, type, time } = value;
        newAvailability[day].slots[index][type] = time;
      }
      return { ...prev, availability: newAvailability };
    });
  };

  const addTimeSlot = (day: string) => {
    setFormData((prev) => {
      const newAvailability = { ...prev.availability };
      newAvailability[day].slots.push({ start: "09:00", end: "18:00" });
      return { ...prev, availability: newAvailability };
    });
  };

  // Image upload handlers
  const handleImageUpload = async (files: FileList) => {
    if (!files || files.length === 0) return;

    const maxImages = 10;
    const maxFileSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif"];

    // Check if adding these files would exceed the limit
    if (formData.images.length + files.length > maxImages) {
      showError(`You can only upload up to ${maxImages} images`);
      return;
    }

    const validFiles: File[] = [];
    const newPreviewUrls: string[] = [];

    // Validate each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      if (!allowedTypes.includes(file.type)) {
        showError(
          `${file.name} is not a valid image format. Please use JPG, PNG, or GIF.`
        );
        continue;
      }

      if (file.size > maxFileSize) {
        showError(`${file.name} is too large. Maximum file size is 5MB.`);
        continue;
      }

      validFiles.push(file);
      newPreviewUrls.push(URL.createObjectURL(file));
    }

    if (validFiles.length === 0) return;

    setUploadingImages(true);
    try {
      // Upload images to backend
      const uploadPromises = validFiles.map(async (file) => {
        const formData = new FormData();
        formData.append("file", file);
        const result = await sdk.uploadImage(formData);
        if (result.error) {
          throw new Error(result.message || "Upload failed");
        }
        return { file, url: result.url };
      });

      const uploadResults = await Promise.all(uploadPromises);

      // Update form data with uploaded files
      setFormData((prev) => ({
        ...prev,
        images: [...prev.images, ...uploadResults.map((r) => r.file)],
      }));

      // Update preview URLs and uploaded URLs
      setImagePreviewUrls((prev) => [...prev, ...newPreviewUrls]);
      setUploadedImageUrls((prev) => [
        ...prev,
        ...uploadResults.map((r) => r.url || ""),
      ]);

      success(`Successfully uploaded ${uploadResults.length} image(s)`);
    } catch (error: any) {
      console.error("Error uploading images:", error);
      showError(error.message || "Failed to upload images");

      // Clean up preview URLs on error
      newPreviewUrls.forEach((url) => URL.revokeObjectURL(url));
    } finally {
      setUploadingImages(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleImageUpload(e.target.files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (files) {
      handleImageUpload(files);
    }
  };

  const removeImage = (index: number) => {
    // Revoke the object URL to free memory
    URL.revokeObjectURL(imagePreviewUrls[index]);

    setFormData((prev) => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));

    setImagePreviewUrls((prev) => prev.filter((_, i) => i !== index));
    setUploadedImageUrls((prev) => prev.filter((_, i) => i !== index));
  };

  const handleNext = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = useCallback(async () => {
    // Validation
    if (!formData.title.trim()) {
      showError("Please enter a listing title");
      return;
    }
    // if (!formData.description.trim()) {
    //   showError("Please enter a description");
    //   return;
    // }
    if (!formData.price.trim() || isNaN(parseFloat(formData.price))) {
      showError("Please enter a valid price");
      return;
    }
    // if (!formData.category) {
    //   showError("Please select a category");
    //   return;
    // }

    setLoading(true);
    try {
      setError(null);

      // Prepare the payload for the new API
      const payload: any = {
        // Basic info
        listingType: formData.listingType,
        title: formData.title.trim(),
        description: formData.description.trim(),
        category: formData.category,
        images: uploadedImageUrls.length > 0 ? uploadedImageUrls : null,

        // Pricing
        price: parseFloat(formData.price),
        discountPrice: formData.discountPrice
          ? parseFloat(formData.discountPrice)
          : null,
        quantity: parseInt(formData.quantity),

        // Location
        addressLine: formData.addressLine,
        city: formData.city,
        country: formData.country,
      };

      // Add item-specific data
      if (formData.listingType === "Item") {
        payload.dimensions = {
          length: formData.length,
          width: formData.width,
          height: formData.height,
          weight: formData.weight,
          lengthUnit: "cm",
          weightUnit: "kg",
        };

        payload.shippingOptions = {
          ebaDelivery: formData.ebaDelivery,
          localDelivery: formData.localDelivery,
          internationalDelivery: formData.internationalDelivery,
          pickup: formData.pickup,
          meetup: formData.meetup,
          fragileHandling: formData.fragileHandling,
          flammableGas: formData.flammableGas,
          liquidItems: formData.liquidItems,
          glassBreakable: formData.glassBreakable,
        };

        // Add sizes for clothing category
        if (formData.category === "clothing") {
          payload.sizes = Object.entries(formData.sizes).map(
            ([size, data]) => ({
              size,
              selected: data.selected,
              quantity: data.quantity,
            })
          );
        }
      }

      // Add service-specific data
      if (formData.listingType === "Service") {
        payload.schedulingType = formData.schedulingType;
        payload.serviceLocationType = formData.serviceLocationType;
        payload.paymentTerms = formData.paymentTerms;

        if (formData.schedulingType === "slots") {
          payload.availability = formData.availability;
        }
      }

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/member/listings",
        method: "POST",
        body: payload,
      });

      console.log("Create Listing API Response:", response);

      if (!response.error) {
        success("Listing created successfully!");
        navigate("/member/listings");
      } else {
        setError(String(response.message || "Failed to create listing"));
        showError(String(response.message || "Failed to create listing"));
      }
    } catch (error: any) {
      console.error("Error creating listing:", error);
      const errorMessage = String(error?.message || "Failed to create listing");
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Step indicator component
  const StepIndicator = ({ currentStep }: { currentStep: number }) => (
    <div className="flex items-start justify-center mb-16">
      {stepData.map((step, index) => {
        const isCompleted = currentStep > step.id;
        const isActive = currentStep === step.id;

        return (
          <Fragment key={step.id}>
            <div className="flex flex-col items-center relative">
              <div className="relative w-12 h-12">
                {isActive || isCompleted ? (
                  <>
                    <div className="absolute inset-0 rounded-full bg-white"></div>
                    <div className="absolute inset-1 rounded-full bg-[#F52D2A] flex items-center justify-center text-white text-xl font-bold">
                      {isCompleted ? (
                        <svg
                          className="w-6 h-6"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="3"
                            d="M5 13l4 4L19 7"
                          ></path>
                        </svg>
                      ) : (
                        step.id
                      )}
                    </div>
                  </>
                ) : (
                  <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-xl font-bold">
                    {step.id}
                  </div>
                )}
              </div>
              <p
                className={`text-center text-sm font-semibold mt-3 absolute -bottom-8 w-24 ${
                  isActive || isCompleted ? "text-[#F52D2A]" : "text-gray-400"
                }`}
              >
                {step.title}
              </p>
            </div>

            {index < stepData.length - 1 && (
              <div className="flex-1 h-1 mt-5">
                {isCompleted ? (
                  <div className="h-full bg-[#F52D2A]"></div>
                ) : isActive ? (
                  <div className="h-full flex">
                    <div className="w-1/2 bg-[#F52D2A]"></div>
                    <div className="w-1/2 bg-gray-300"></div>
                  </div>
                ) : (
                  <div className="h-full bg-gray-300"></div>
                )}
              </div>
            )}
          </Fragment>
        );
      })}
    </div>
  );

  return (
    <MemberWrapper>
      <>
        <div className="min-h-screen bg-[#0D3166] text-white p-6">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <InteractiveButton
                onClick={() => navigate("/member/listings")}
                className="text-white hover:text-gray-300 mb-4 flex items-center !p-0"
              >
                ← Back to Listings
              </InteractiveButton>
              <InteractiveButton
                type="button"
                onClick={handlePrefill}
                className="ml-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Prefill
              </InteractiveButton>
            </div>
            <h1 className="text-3xl font-bold text-white mt-4">
              Create New Listing
            </h1>
          </div>
          {/* Error Display */}
          {error && (
            <div className="max-w-5xl mx-auto mb-6">
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex items-center">
                  <div className="text-red-400 mr-3">
                    <svg
                      className="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-red-800">Error</h3>
                    <p className="text-sm text-red-700 mt-1">{error}</p>
                  </div>
                  <button
                    onClick={() => setError(null)}
                    className="ml-auto text-red-400 hover:text-red-600"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Main Content */}
          <div className="max-w-5xl mx-auto">
            <div className="mb-12">
              <StepIndicator currentStep={currentStep} />
            </div>

            <div className="space-y-6">
              {/* Section 1: Listing Details */}
              <div
                ref={sectionRefs[0]}
                className="bg-white rounded-lg shadow-sm text-black p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  1. Listing Details
                </h2>
                <div className="space-y-6">
                  {/* Listing Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Listing Type*
                    </label>
                    <p className="text-sm text-gray-500 mb-2">
                      Select whether you're listing an item or service
                    </p>
                    <select
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                      value={formData.listingType}
                      onChange={(e) =>
                        handleInputChange("listingType", e.target.value)
                      }
                    >
                      <option value="Item">Item</option>
                      <option value="Service">Service</option>
                    </select>
                  </div>

                  {/* Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Title*
                    </label>
                    <p className="text-sm text-gray-500 mb-2">
                      (Short name of the item or service required)
                    </p>
                    <MkdInputV2
                      placeholder="3D Camera for Sale"
                      value={formData.title}
                      onChange={(e) =>
                        handleInputChange("title", e.target.value)
                      }
                      required
                    >
                      <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59]" />
                    </MkdInputV2>
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description*
                    </label>
                    <p className="text-sm text-gray-500 mb-2">
                      (Detailed information about the item required)
                    </p>
                    <textarea
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent resize-none"
                      rows={6}
                      placeholder="Describe your item in detail, including condition, features, etc."
                      value={formData.description}
                      onChange={(e) =>
                        handleInputChange("description", e.target.value)
                      }
                      required
                    />
                  </div>

                  {/* Category */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category*{" "}
                      <button
                        type="button"
                        onClick={() => setIsSuggestModalOpen(true)}
                        className="text-blue-600 cursor-pointer text-sm font-medium hover:underline"
                      >
                        Suggest Category
                      </button>
                    </label>
                    <p className="text-sm text-gray-500 mb-2">
                      Select the most relevant category for your item
                    </p>
                    <select
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                      value={formData.category}
                      onChange={(e) =>
                        handleInputChange("category", e.target.value)
                      }
                      required
                    >
                      {categories.map((cat) => (
                        <option key={cat.value} value={cat.value}>
                          {cat.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Available Sizes */}
                  {formData.category === "clothing" && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Available Sizes
                      </label>
                      <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                        {availableSizes.map((size) => (
                          <div key={size} className="flex items-center">
                            <input
                              type="checkbox"
                              id={`size-${size}`}
                              checked={formData.sizes[size]?.selected || false}
                              onChange={(e) =>
                                handleSizeChange(
                                  size,
                                  "selected",
                                  e.target.checked
                                )
                              }
                              className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label
                              htmlFor={`size-${size}`}
                              className="text-sm text-gray-700 w-10"
                            >
                              {size}
                            </label>
                            <MkdInputV2
                              placeholder="Quantity"
                              type="number"
                              value={formData.sizes[size]?.quantity || ""}
                              onChange={(e) =>
                                handleSizeChange(
                                  size,
                                  "quantity",
                                  e.target.value
                                )
                              }
                              disabled={!formData.sizes[size]?.selected}
                              className="w-full"
                            >
                              <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] !text-sm" />
                            </MkdInputV2>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Upload Images */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Upload images*
                    </label>
                    <p className="text-sm text-gray-500 mb-2">
                      Add up to 10 images of your item. First image will be the
                      cover image.
                    </p>

                    {/* Hidden file input */}
                    <input
                      ref={fileInputRef}
                      type="file"
                      multiple
                      accept="image/jpeg,image/jpg,image/png,image/gif"
                      onChange={handleFileInputChange}
                      className="hidden"
                    />

                    {/* Drop zone */}
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 cursor-pointer transition-colors"
                      onDragOver={handleDragOver}
                      onDragEnter={handleDragEnter}
                      onDrop={handleDrop}
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <div className="flex flex-col items-center">
                        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                          {uploadingImages ? (
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-600"></div>
                          ) : (
                            <svg
                              className="w-6 h-6 text-gray-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                              />
                            </svg>
                          )}
                        </div>
                        <p className="text-gray-600 mb-2">
                          {uploadingImages
                            ? "Uploading images..."
                            : "Drag and drop images here, or click to browse"}
                        </p>
                        <p className="text-sm text-gray-500">
                          JPG, PNG or GIF, max 5MB each
                        </p>
                      </div>
                    </div>

                    <div className="text-center mt-4">
                      <InteractiveButton
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploadingImages}
                        className="bg-gray-800 text-white px-4 py-2 rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {uploadingImages ? "Uploading..." : "Upload Images"}
                      </InteractiveButton>
                    </div>

                    {/* Uploaded images preview */}
                    {imagePreviewUrls.length > 0 && (
                      <div className="flex gap-3 mt-4 flex-wrap">
                        {imagePreviewUrls.map((url, index) => (
                          <div key={index} className="relative group">
                            <div className="w-24 h-24 bg-gray-200 rounded-lg overflow-hidden">
                              <img
                                src={url}
                                alt={`Uploaded image ${index + 1}`}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <button
                              onClick={() => removeImage(index)}
                              className="absolute -top-1 -right-1 w-5 h-5 bg-red-600 text-white rounded-full text-xs hover:bg-red-700 flex items-center justify-center"
                            >
                              ×
                            </button>
                            {index === 0 && (
                              <div className="absolute bottom-0 left-0 right-0 bg-blue-600 text-white text-xs text-center py-1 rounded-b-lg">
                                Cover
                              </div>
                            )}
                          </div>
                        ))}

                        {/* Add more images button */}
                        {imagePreviewUrls.length < 10 && (
                          <div
                            onClick={() => fileInputRef.current?.click()}
                            className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center text-gray-400 hover:border-gray-400 cursor-pointer"
                          >
                            <span className="text-3xl">+</span>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Image count indicator */}
                    <p className="text-sm text-gray-500 mt-2">
                      {formData.images.length} / 10 images uploaded
                    </p>
                  </div>
                </div>
              </div>

              {/* Section 2: Pricing */}
              <div
                ref={sectionRefs[1]}
                className="bg-white rounded-lg shadow-sm text-black p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  2.{" "}
                  {formData.listingType === "Service" ? "Pricing" : "Pricing"}
                </h2>
                <div className="space-y-6">
                  {/* Price in eBa$ */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price in eBa$*
                    </label>
                    <p className="text-sm text-gray-500 mb-2">
                      Set the price for your{" "}
                      {formData.listingType === "Service" ? "service" : "item"}{" "}
                      in eBa currency
                    </p>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        eBa$
                      </span>
                      <MkdInputV2
                        type="number"
                        placeholder="0.00"
                        value={formData.price}
                        onChange={(e) =>
                          handleInputChange("price", e.target.value)
                        }
                        required
                      >
                        <MkdInputV2.Field
                          className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] pl-12"
                          step="0.01"
                          min="0"
                        />
                      </MkdInputV2>
                    </div>
                  </div>

                  {/* Optional Discount Price */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Optional Discount Price
                    </label>
                    <p className="text-sm text-gray-500 mb-2">
                      Set a discounted price if you're offering a special deal
                    </p>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        eBa$
                      </span>
                      <MkdInputV2
                        type="number"
                        placeholder="0.00"
                        value={formData.discountPrice}
                        onChange={(e) =>
                          handleInputChange("discountPrice", e.target.value)
                        }
                      >
                        <MkdInputV2.Field
                          className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] pl-12"
                          step="0.01"
                          min="0"
                        />
                      </MkdInputV2>
                    </div>
                  </div>

                  {/* Conditional Fields: Item vs. Service */}
                  {formData.listingType === "Item" ? (
                    // Quantity for Item
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Quantity Available
                      </label>
                      <p className="text-sm text-gray-500 mb-2">
                        Number of items you have for sale
                      </p>
                      <MkdInputV2
                        type="number"
                        placeholder="1"
                        value={formData.quantity}
                        onChange={(e) =>
                          handleInputChange("quantity", e.target.value)
                        }
                        required
                      >
                        <MkdInputV2.Field
                          className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59]"
                          min="1"
                        />
                      </MkdInputV2>
                    </div>
                  ) : (
                    // Service Scheduling
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Service Scheduling*
                      </label>
                      <p className="text-sm text-gray-500 mb-3">
                        How do you prefer to schedule your service?
                      </p>
                      <div className="space-y-2">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="schedulingType"
                            value="slots"
                            checked={formData.schedulingType === "slots"}
                            onChange={() =>
                              handleInputChange("schedulingType", "slots")
                            }
                            className="mr-2"
                          />
                          I offer service in specific time slots
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="schedulingType"
                            value="flexible"
                            checked={formData.schedulingType === "flexible"}
                            onChange={() =>
                              handleInputChange("schedulingType", "flexible")
                            }
                            className="mr-2"
                          />
                          I'm available anytime (flexible scheduling)
                        </label>
                      </div>

                      {formData.schedulingType === "slots" && (
                        <div className="mt-4 space-y-3">
                          <label className="block text-sm font-medium text-gray-700">
                            Service Availability*
                          </label>
                          <p className="text-sm text-gray-500 mb-3">
                            Select the days and time slots you are available
                          </p>
                          {daysOfWeek.map((day) => (
                            <div key={day} className="flex items-center gap-4">
                              <input
                                type="checkbox"
                                checked={formData.availability[day].selected}
                                onChange={(e) =>
                                  handleAvailabilityChange(
                                    day,
                                    "selected",
                                    e.target.checked
                                  )
                                }
                              />
                              <span className="w-24">{day}</span>
                              {formData.availability[day].slots.map(
                                (slot, index) => (
                                  <div
                                    key={index}
                                    className="flex items-center"
                                  >
                                    <input
                                      type="time"
                                      value={slot.start}
                                      onChange={(e) =>
                                        handleAvailabilityChange(day, "slot", {
                                          index,
                                          type: "start",
                                          time: e.target.value,
                                        })
                                      }
                                      className="border border-gray-300 rounded-md p-1"
                                    />
                                    <span className="mx-2">–</span>
                                    <input
                                      type="time"
                                      value={slot.end}
                                      onChange={(e) =>
                                        handleAvailabilityChange(day, "slot", {
                                          index,
                                          type: "end",
                                          time: e.target.value,
                                        })
                                      }
                                      className="border border-gray-300 rounded-md p-1"
                                    />
                                  </div>
                                )
                              )}
                              <InteractiveButton
                                onClick={() => addTimeSlot(day)}
                                className="text-sm !p-1"
                              >
                                + Add Time Slot
                              </InteractiveButton>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Section 3: Location / Service Area */}
              <div
                ref={sectionRefs[2]}
                className="bg-white rounded-lg shadow-sm text-black p-6"
              >
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">
                    3.{" "}
                    {formData.listingType === "Service"
                      ? "Service Area"
                      : "Location of the Item"}
                  </h2>
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-1"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Fill from Location
                  </button>
                </div>
                <p className="text-sm text-gray-500 mb-6 -mt-4">
                  {formData.listingType === "Service"
                    ? "Specify the area where you provide your service"
                    : "This is pre-filled from your profile address"}
                </p>

                {formData.listingType === "Service" && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Service Location Type*
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="serviceLocationType"
                          value="provider"
                          checked={formData.serviceLocationType === "provider"}
                          onChange={() =>
                            handleInputChange("serviceLocationType", "provider")
                          }
                          className="mr-2"
                        />
                        I provide service at my location
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="serviceLocationType"
                          value="buyer"
                          checked={formData.serviceLocationType === "buyer"}
                          onChange={() =>
                            handleInputChange("serviceLocationType", "buyer")
                          }
                          className="mr-2"
                        />
                        I go to buyer's location
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="serviceLocationType"
                          value="choice"
                          checked={formData.serviceLocationType === "choice"}
                          onChange={() =>
                            handleInputChange("serviceLocationType", "choice")
                          }
                          className="mr-2"
                        />
                        Buyer can choose
                      </label>
                    </div>
                  </div>
                )}

                <div className="space-y-6">
                  {/* Address Line */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {formData.listingType === "Service"
                        ? "Service Area Address"
                        : "Address Line"}
                    </label>
                    <MkdInputV2
                      placeholder={
                        formData.listingType === "Service"
                          ? "123 Tech Hub Street, Suite 200"
                          : "123 Main Street, Apt 4B"
                      }
                      value={formData.addressLine}
                      onChange={(e) =>
                        handleInputChange("addressLine", e.target.value)
                      }
                      required
                    >
                      <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59]" />
                    </MkdInputV2>
                  </div>

                  {/* City */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      City
                    </label>
                    <MkdInputV2
                      placeholder={
                        formData.listingType === "Service"
                          ? "San Francisco"
                          : "New York"
                      }
                      value={formData.city}
                      onChange={(e) =>
                        handleInputChange("city", e.target.value)
                      }
                      required
                    >
                      <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59]" />
                    </MkdInputV2>
                  </div>

                  {/* Country */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Country
                    </label>
                    <MkdInputV2
                      placeholder="United States"
                      value={formData.country}
                      onChange={(e) =>
                        handleInputChange("country", e.target.value)
                      }
                      required
                    >
                      <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59]" />
                    </MkdInputV2>
                  </div>
                </div>
              </div>

              {/* Section 4: Shipping Options / Service Details */}
              <div
                ref={sectionRefs[3]}
                className="bg-white rounded-lg shadow-sm text-black p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  4.{" "}
                  {formData.listingType === "Service"
                    ? "Service Details"
                    : "Shipping Options"}
                </h2>
                {formData.listingType === "Item" ? (
                  <div className="space-y-8">
                    {/* Package Dimensions */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Package Dimensions*
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            Length
                          </label>
                          <div className="flex">
                            <MkdInputV2
                              type="number"
                              placeholder="0.0"
                              value={formData.length}
                              onChange={(e) =>
                                handleInputChange("length", e.target.value)
                              }
                              required
                            >
                              <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] rounded-r-none" />
                            </MkdInputV2>
                            <select className="border border-l-0 border-gray-300 rounded-r-md px-2 py-2 bg-gray-50 text-sm">
                              <option>cm</option>
                              <option>in</option>
                            </select>
                          </div>
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            Width
                          </label>
                          <div className="flex">
                            <MkdInputV2
                              type="number"
                              placeholder="0.0"
                              value={formData.width}
                              onChange={(e) =>
                                handleInputChange("width", e.target.value)
                              }
                              required
                            >
                              <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] rounded-r-none" />
                            </MkdInputV2>
                            <select className="border border-l-0 border-gray-300 rounded-r-md px-2 py-2 bg-gray-50 text-sm">
                              <option>cm</option>
                              <option>in</option>
                            </select>
                          </div>
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            Height
                          </label>
                          <div className="flex">
                            <MkdInputV2
                              type="number"
                              placeholder="0.0"
                              value={formData.height}
                              onChange={(e) =>
                                handleInputChange("height", e.target.value)
                              }
                              required
                            >
                              <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] rounded-r-none" />
                            </MkdInputV2>
                            <select className="border border-l-0 border-gray-300 rounded-r-md px-2 py-2 bg-gray-50 text-sm">
                              <option>cm</option>
                              <option>in</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Package Weight */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Package Weight*
                      </label>
                      <div className="flex max-w-xs">
                        <MkdInputV2
                          type="number"
                          placeholder="0.0"
                          value={formData.weight}
                          onChange={(e) =>
                            handleInputChange("weight", e.target.value)
                          }
                          required
                        >
                          <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] rounded-r-none" />
                        </MkdInputV2>
                        <select className="border border-l-0 border-gray-300 rounded-r-md px-2 py-2 bg-gray-50 text-sm">
                          <option>kg</option>
                          <option>lb</option>
                        </select>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        = 0.0 kg (0.0 lb)
                      </p>
                    </div>

                    {/* Package Handling Information */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Package Handling Information
                      </label>
                      <div className="space-y-3">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.fragileHandling}
                            onChange={(e) =>
                              handleInputChange(
                                "fragileHandling",
                                e.target.checked
                              )
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="text-sm text-gray-700">
                            Fragile (handle with care)
                          </span>
                          <span className="ml-2 text-gray-400 cursor-pointer">
                            ⓘ
                          </span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.flammableGas}
                            onChange={(e) =>
                              handleInputChange(
                                "flammableGas",
                                e.target.checked
                              )
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="text-sm text-gray-700">
                            Contains Flammable Gas (e.g., aerosols, gas
                            canisters)
                          </span>
                          <span className="ml-2 text-gray-400 cursor-pointer">
                            ⓘ
                          </span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.liquidItems}
                            onChange={(e) =>
                              handleInputChange("liquidItems", e.target.checked)
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="text-sm text-gray-700">
                            Liquid (e.g., bottles, gels, oils)
                          </span>
                          <span className="ml-2 text-gray-400 cursor-pointer">
                            ⓘ
                          </span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.glassBreakable}
                            onChange={(e) =>
                              handleInputChange(
                                "glassBreakable",
                                e.target.checked
                              )
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="text-sm text-gray-700">
                            Glass (breakable material)
                          </span>
                          <span className="ml-2 text-gray-400 cursor-pointer">
                            ⓘ
                          </span>
                        </label>
                      </div>
                      <p className="text-xs text-gray-500 mt-3">
                        This information is visible to delivery agents to ensure
                        safe and appropriate handling of your item.
                      </p>
                    </div>

                    {/* Choose Delivery Methods */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Choose Delivery Methods*
                      </label>
                      <p className="text-sm text-gray-500 mb-4">
                        Select all applicable shipping methods
                      </p>
                      <div className="space-y-4">
                        <label className="flex items-start p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={formData.ebaDelivery}
                            onChange={(e) =>
                              handleInputChange("ebaDelivery", e.target.checked)
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div>
                            <span className="text-sm font-medium text-gray-800">
                              eBa Delivery
                            </span>
                            <p className="text-xs text-gray-500">
                              Pre-paid, platform-managed shipping with QR code
                              scan verification
                            </p>
                          </div>
                        </label>
                        <label className="flex items-start p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={formData.localDelivery}
                            onChange={(e) =>
                              handleInputChange(
                                "localDelivery",
                                e.target.checked
                              )
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div>
                            <span className="text-sm font-medium text-gray-800">
                              Local Delivery
                            </span>
                            <p className="text-xs text-gray-500">
                              Same-day delivery arranged by you (car, bike
                              courier, etc.)
                            </p>
                          </div>
                        </label>
                        <label className="flex items-start p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={formData.internationalDelivery}
                            onChange={(e) =>
                              handleInputChange(
                                "internationalDelivery",
                                e.target.checked
                              )
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div>
                            <span className="text-sm font-medium text-gray-800">
                              International Delivery
                            </span>
                            <p className="text-xs text-gray-500">
                              Global shipping using FedEx, DHL, UPS, etc.
                            </p>
                          </div>
                        </label>
                        <label className="flex items-start p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={formData.pickup}
                            onChange={(e) =>
                              handleInputChange("pickup", e.target.checked)
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div>
                            <span className="text-sm font-medium text-gray-800">
                              Pickup
                            </span>
                            <p className="text-xs text-gray-500">
                              Buyer picks up the item from your location
                            </p>
                          </div>
                        </label>
                        <label className="flex items-start p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={formData.meetup}
                            onChange={(e) =>
                              handleInputChange("meetup", e.target.checked)
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div>
                            <span className="text-sm font-medium text-gray-800">
                              Meet-up
                            </span>
                            <p className="text-xs text-gray-500">
                              Meet at a public place to complete the transaction
                            </p>
                          </div>
                        </label>
                      </div>
                    </div>

                    {/* Listing Expiration */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center text-sm text-gray-600">
                        <span className="mr-2">🗓️</span>
                        <span>
                          Listing will expire in 30 days (May 22, 2023)
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1 ml-6">
                        You will receive a reminder before expiration. You can
                        reactivate or update later.
                      </p>
                    </div>
                  </div>
                ) : (
                  // Service Details
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Terms*
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="paymentTerms"
                          value="before"
                          checked={formData.paymentTerms === "before"}
                          onChange={() =>
                            handleInputChange("paymentTerms", "before")
                          }
                          className="mr-2"
                        />
                        Pay before service
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="paymentTerms"
                          value="after"
                          checked={formData.paymentTerms === "after"}
                          onChange={() =>
                            handleInputChange("paymentTerms", "after")
                          }
                          className="mr-2"
                        />
                        Pay after service
                      </label>
                    </div>
                  </div>
                )}
              </div>

              {/* Section 5: Final Actions */}
              <div
                ref={sectionRefs[4]}
                className="bg-white rounded-lg shadow-sm text-black p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  5. Final Actions
                </h2>

                <p className="text-sm text-gray-500 mb-6">
                  Please fill in all required fields (marked with *) to create
                  your listing.
                </p>

                <div className="flex flex-col md:flex-row gap-4">
                  <InteractiveButton
                    type="button"
                    className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-50 font-semibold"
                  >
                    💾 Save as Draft
                  </InteractiveButton>
                  <InteractiveButton
                    type="button"
                    onClick={handleSubmit}
                    disabled={loading || justPrefilled}
                    className="flex-1 bg-[#F52D2A] text-white py-3 px-6 rounded-md hover:bg-red-600 disabled:bg-gray-400 disabled:cursor-not-allowed font-semibold"
                  >
                    {loading ? "Creating..." : "✓ Create Listing"}
                  </InteractiveButton>
                </div>
                <div className="text-center mt-4">
                  <InteractiveButton
                    type="button"
                    className="text-gray-600 hover:text-gray-800"
                  >
                    Cancel
                  </InteractiveButton>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Modal
          isOpen={isSuggestModalOpen}
          modalCloseClick={() => setIsSuggestModalOpen(false)}
          title=""
          modalHeader={false}
          classes={{
            modal: "flex items-center justify-center",
            modalDialog:
              "bg-white rounded-lg shadow-xl p-8 w-full max-w-lg mx-auto text-black",
          }}
        >
          <div className="flex justify-between items-start mb-4">
            <h2 className="text-2xl font-bold text-gray-800">
              Suggest category
            </h2>
            <button
              onClick={() => setIsSuggestModalOpen(false)}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              &times;
            </button>
          </div>
          <p className="text-gray-600 mb-6">
            Add the name of the category you want to suggest. Our team will
            review your suggestion.
          </p>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Name
            </label>
            <MkdInputV2
              placeholder="Sustainable Products"
              value={suggestedCategory}
              onChange={(e) => setSuggestedCategory(e.target.value)}
            >
              <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0D3166]" />
            </MkdInputV2>
          </div>
          <div className="mt-8 flex justify-end gap-4">
            <InteractiveButton
              onClick={() => setIsSuggestModalOpen(false)}
              className="border border-gray-300 text-gray-800 px-6 py-2 rounded-md hover:bg-gray-100 font-semibold"
            >
              Cancel
            </InteractiveButton>
            <InteractiveButton
              onClick={handleSuggestCategory}
              className="bg-[#0D3166] text-white px-6 py-2 rounded-md hover:bg-blue-800 font-semibold"
            >
              Submit
            </InteractiveButton>
          </div>
        </Modal>
      </>
    </MemberWrapper>
  );
};

export default MemberAddListingPage;
